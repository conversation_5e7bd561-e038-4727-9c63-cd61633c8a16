spring:
  # DB configuration
  datasource:
    password: ## ENV Inject

  # Redis configuration
  data:
    redis:
      host: localhost

spm:
  web-ui:
    summaryReportUrl: https://spm-demo/report/result/template/
    pmResultUrl: https://spm-demo/report/result/performance-metric/
    detectorResultUrl: https://spm-demo/report/result/detector-report/

  user-mgmt-service:
    endpoint: http://localhost:9100/user-service

  ai-service:
    endpoint: http://localhost:5000

studio:
  user-service:
    endpoint: http://localhost:8081
  agency-service:
    endpoint: http://localhost:8082

data-hub:
  integration-service:
    endpoint: http://localhost:9000/integration
