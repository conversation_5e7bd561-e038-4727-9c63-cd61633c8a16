package com.siemens.spm.analysis.detectorreport.template.aggregation;

import com.siemens.spm.analysis.domain.DetectorTemplate;
import com.siemens.spm.analysis.strategy.DetectorReportStrategy;
import com.siemens.spm.analysis.vo.detectorreport.DetectorReportChartVO;
import com.siemens.spm.usermanagementservice.api.vo.IntersectionInternalVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;
import java.util.stream.Collectors;

/**
 * Strategy for daily aggregation of detector reports.
 * Processes data in daily intervals for days specified in the template.
 */
@Component
@Slf4j
public class DailyAggregationStrategy extends BaseAggregationStrategy {

    public DailyAggregationStrategy(DetectorReportStrategy detectorReportStrategy) {
        super(detectorReportStrategy);
    }

    @Override
    public List<DetectorReportChartVO> aggregatePeriods(DetectorTemplate template,
                                                        IntersectionInternalVO intersection,
                                                        List<LocalDateTime> targetDateTimes) {

        List<DetectorReportChartVO> resultCharts = createEmptyResultList();


        List<Long> filterDetectorIds = getFilterDetectorIds(template.getDetectorsJson());
        Integer agencyId = template.getAgencyId();
        String intersectionId = intersection.getId();
        for (Pair<LocalDateTime, LocalDateTime> targetPeriod : targetPeriods) {
            LocalDateTime startDateTime = targetPeriod.getFirst();
            LocalDateTime endDateTime = targetPeriod.getSecond();

            log.info("Processing hourly data with time range: {} to {}", startDateTime, endDateTime);
            LocalDateTime fromTime = targetPeriod.getFirst();
            LocalDateTime toTime = targetPeriod.getSecond();
            detectorReportAnalysis(agencyId, intersectionId, fromTime, toTime, filterDetectorIds).ifPresent(
                    detectorReportChartVO -> {
                        detectorReportChartVO.setFromTime(fromTime);
                        detectorReportChartVO.setToTime(toTime);
                        resultCharts.add(detectorReportChartVO);
                    });
            log.debug("End analysis daily detector report from {} to {}", fromTime, toTime);
        }

        return resultCharts;
    }

    /**
     * Splits a period into daily sub-periods
     * @param targetDateTimes The list of target date times
     * @return daily sub-periods for this period
     */
    private Map<LocalDate, Pair<LocalDateTime, LocalDateTime>> splitPeriodIntoDaily(List<LocalDateTime> targetDateTimes) {
        Map<LocalDate, List<LocalDateTime>> groupedByDate = targetDateTimes.stream()
                .distinct()
                .collect(Collectors.groupingBy(
                        LocalDateTime::toLocalDate,
                        TreeMap::new,
                        Collectors.toList()
                ));

        Pair<LocalDateTime, LocalDateTime> dailyPeriods = new ArrayList<>();

        for (Map.Entry<LocalDate, List<LocalDateTime>> entry : groupedByDate.entrySet()) {
            LocalDate date = entry.getKey();
            List<LocalDateTime> dayHours = entry.getValue();

            dayHours.sort(null);

            LocalDateTime dayStart = dayHours.get(0);
            LocalDateTime dayEnd = dayHours.get(dayHours.size() - 1);

            dailyPeriods.add(new DailyPeriod(date, dayStart, dayEnd, dayHours));
    }

}