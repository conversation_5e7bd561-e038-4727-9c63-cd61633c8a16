package com.siemens.spm.analysis.detectorreport.template.aggregation;

import com.siemens.spm.analysis.domain.DetectorTemplate;
import com.siemens.spm.analysis.strategy.DetectorReportStrategy;
import com.siemens.spm.analysis.vo.detectorreport.DetectorReportChartVO;
import com.siemens.spm.usermanagementservice.api.vo.IntersectionInternalVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Strategy for hourly aggregation of detector reports.
 * Processes data in hourly intervals for days specified in the template.
 *
 * <AUTHOR> <PERSON>am
 * @version 1.0
 * @since 01/07/2025
 */
@Component
@Slf4j
public class HourlyAggregationStrategy extends BaseAggregationStrategy {

    private static final int HOUR_INTERVAL = 1;

    public HourlyAggregationStrategy(DetectorReportStrategy detectorReportStrategy) {
        super(detectorReportStrategy);
    }

    @Override
    public List<DetectorReportChartVO> aggregatePeriods(DetectorTemplate template,
                                                        IntersectionInternalVO intersection,
                                                        List<Pair<LocalDateTime, LocalDateTime>> targetPeriods) {
        List<DetectorReportChartVO> resultCharts = createEmptyResultList();

        List<Long> filterDetectorIds = getFilterDetectorIds(template.getDetectorsJson());
        for (Pair<LocalDateTime, LocalDateTime> targetPeriod : targetPeriods) {
            LocalDateTime startDateTime = targetPeriod.getFirst();
            LocalDateTime endDateTime = targetPeriod.getSecond();

            log.info("Processing hourly data with time range: {} to {}", startDateTime, endDateTime);
            LocalDateTime iterDateTime = startDateTime;
            while (!iterDateTime.isAfter(endDateTime)) {
                LocalDateTime nextHour = iterDateTime.plusHours(HOUR_INTERVAL);
                log.debug("Start analysis hourly detector report from {} to {}", iterDateTime, nextHour);
                buildHourlyResultCharts(template, intersection, iterDateTime, nextHour, filterDetectorIds, resultCharts);
                iterDateTime = nextHour;
            }
        }

        return resultCharts;
    }

    private void buildHourlyResultCharts(DetectorTemplate template,
                           IntersectionInternalVO intersection,
                           LocalDateTime iterDateTime,
                           LocalDateTime nextHour,
                           List<Long> filterDetectorIds,
                           List<DetectorReportChartVO> resultCharts) {
        detectorReportAnalysis(template.getAgencyId(), intersection.getId(), iterDateTime, nextHour,
                filterDetectorIds).ifPresent(detectorReportChartVO -> {
            detectorReportChartVO.setFromTime(iterDateTime);
            detectorReportChartVO.setToTime(nextHour);
            resultCharts.add(detectorReportChartVO);
        });
    }

}