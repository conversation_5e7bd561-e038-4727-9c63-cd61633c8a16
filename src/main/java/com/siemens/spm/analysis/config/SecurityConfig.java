/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : SecurityConfig.java
 * Project     : SPM Platform
 */
package com.siemens.spm.analysis.config;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpStatus;
import org.springframework.security.config.Customizer;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;
import org.springframework.security.config.annotation.web.configurers.HeadersConfigurer;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.HttpStatusEntryPoint;
import org.springframework.security.web.util.matcher.AntPathRequestMatcher;
import org.springframework.web.cors.CorsConfigurationSource;

/**
 * Enable security configuration. This annotation denotes configuration for
 * spring security.
 */
@Configuration
@EnableWebSecurity
public class SecurityConfig {

    @Autowired
    @Qualifier(CorsConfig.CORS_CONFIG_BEAN_NAME)
    private CorsConfigurationSource corsConfigurationSource;

    private static final List<String> AUTH_WHITE_LIST = List.of(
            "/actuator/**", "/configuration/**",
            "/v2/api-docs", "/v3/api-docs/**",
            "/swagger-ui/**", "/swagger*/**", "/webjars/**"
    );

    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
        AntPathRequestMatcher[] authWhiteListAntPathRequestMatchers = AUTH_WHITE_LIST.stream()
                .map(AntPathRequestMatcher::antMatcher)
                .toArray(AntPathRequestMatcher[]::new);

        http
                .csrf(AbstractHttpConfigurer::disable)
                .headers(headersConfigurer -> headersConfigurer
                        // to make accessible h2 console, it works as frame
                        .frameOptions(HeadersConfigurer.FrameOptionsConfig::disable)
                )
                .cors(corsConfigurer -> corsConfigurer.configurationSource(corsConfigurationSource))
                .sessionManagement(sessionMgmtConfigurer -> sessionMgmtConfigurer
                        .sessionCreationPolicy(SessionCreationPolicy.STATELESS)
                )
                .authorizeHttpRequests(authorize -> authorize
                        .requestMatchers(authWhiteListAntPathRequestMatchers).permitAll()
                        .requestMatchers(AntPathRequestMatcher.antMatcher("/internal-api/**")).permitAll()
                        .anyRequest().authenticated()
                )
                .exceptionHandling(e -> e.authenticationEntryPoint(new HttpStatusEntryPoint(HttpStatus.UNAUTHORIZED)))
                .oauth2ResourceServer(oauth2ResourceServerConfigurer -> oauth2ResourceServerConfigurer
                        .jwt(Customizer.withDefaults())
                );

        return http.build();
    }

}
