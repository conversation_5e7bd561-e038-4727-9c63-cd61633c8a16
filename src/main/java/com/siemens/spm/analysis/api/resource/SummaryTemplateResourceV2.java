package com.siemens.spm.analysis.api.resource;

import java.sql.Timestamp;
import java.time.DayOfWeek;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.RestController;

import com.siemens.spm.analysis.api.boundary.SummaryReportTemplateService;
import com.siemens.spm.analysis.api.controller.SummaryTemplateControllerV2;
import com.siemens.spm.analysis.api.vo.TemplateScheduleVO;
import com.siemens.spm.analysis.api.vo.enums.TemplateAggregation;
import com.siemens.spm.analysis.api.vo.enums.TemplateStatus;
import com.siemens.spm.analysis.api.vo.request.IntersectionIdsRequestVO;
import com.siemens.spm.analysis.api.vo.request.TemplateIntersectionSearchRequestVO;
import com.siemens.spm.analysis.api.vo.request.summaryreport.SummaryTemplateActivateRequestVO;
import com.siemens.spm.analysis.api.vo.request.summaryreport.SummaryTemplateCreateRequestVO;
import com.siemens.spm.analysis.api.vo.request.summaryreport.SummaryTemplateDeleteRequestVO;
import com.siemens.spm.analysis.api.vo.request.summaryreport.SummaryTemplateSearchRequestVO;
import com.siemens.spm.analysis.api.vo.request.summaryreport.SummaryTemplateUpdateCoreDataRequestVO;
import com.siemens.spm.analysis.api.vo.response.IntersectionSearchResultObject;
import com.siemens.spm.analysis.api.vo.response.TemplateScheduleResultObject;
import com.siemens.spm.analysis.api.vo.response.summaryreport.SummaryMetricResultObject;
import com.siemens.spm.analysis.api.vo.response.summaryreport.SummaryTemplateCoreDataResultObject;
import com.siemens.spm.analysis.api.vo.response.summaryreport.SummaryTemplateDetailResultObject;
import com.siemens.spm.analysis.api.vo.response.summaryreport.SummaryTemplateManipulateResultObject;
import com.siemens.spm.analysis.api.vo.response.summaryreport.SummaryTemplateProcessResultObject;
import com.siemens.spm.analysis.api.vo.response.summaryreport.SummaryTemplateProcessResultObject.StatusCode;
import com.siemens.spm.analysis.api.vo.response.summaryreport.SummaryTemplateSearchResultObject;
import com.siemens.spm.common.shared.domaintype.IntersectionStatus;
import com.siemens.spm.common.shared.vo.SimpleResultObject;
import com.siemens.spm.common.util.ResponseUtil;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@RestController
public class SummaryTemplateResourceV2 implements SummaryTemplateControllerV2 {

    private static final String ERROR_OCCUR_MESSAGE = "Error Occurs";

    @Autowired
    private SummaryReportTemplateService summaryReportTemplateService;

    /**
     * {@inheritDoc}
     */
    @Override
    @PreAuthorize("hasAnyLicenses(#agencyId, 'report') " +
            "&& hasPermission('summary-report/templates', 'CREATE', #agencyId, null)")
    public ResponseEntity<SummaryTemplateDetailResultObject> create(Integer agencyId,
                                                                    SummaryTemplateCreateRequestVO requestVO) {
        SummaryTemplateDetailResultObject resultObject;

        try {
            requestVO.setAgencyId(agencyId);
            resultObject = summaryReportTemplateService.createReportTemplate(requestVO);
        } catch (Exception ex) {
            resultObject = new SummaryTemplateDetailResultObject(null,
                    SummaryTemplateDetailResultObject.StatusCode.ERROR);
            log.error(ERROR_OCCUR_MESSAGE, ex);
        }

        return ResponseUtil.wrapOrNotFound(resultObject);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    @PreAuthorize("hasAnyLicenses(#agencyId, 'report') " +
            "&& hasPermission('summary-report/templates', 'READ', #agencyId, null)")
    public ResponseEntity<IntersectionSearchResultObject> searchAvailableTemplateIntersections(Integer agencyId,
                                                                                               Long templateId,
                                                                                               String[] excludeIntIds,
                                                                                               String text,
                                                                                               String[] sort,
                                                                                               Boolean pagination,
                                                                                               Integer page,
                                                                                               Integer size) {
        IntersectionSearchResultObject resultObject;

        TemplateIntersectionSearchRequestVO searchRequestVO = TemplateIntersectionSearchRequestVO.builder()
                .agencyId(agencyId)
                .templateId(templateId)
                .orderByColumns(sort)
                .status(IntersectionStatus.AVAILABLE.getInsight())
                .excludeIntIds(excludeIntIds)
                .text(text)
                .shouldPaginate(pagination)
                .page(page)
                .size(size)
                .build();

        try {
            resultObject = summaryReportTemplateService.searchAvailableTemplateInterSections(searchRequestVO);
        } catch (Exception exception) {
            resultObject = new IntersectionSearchResultObject(null, IntersectionSearchResultObject.StatusCode.ERROR);
            log.error(ERROR_OCCUR_MESSAGE, exception);
        }

        return ResponseUtil.wrapOrNotFound(resultObject);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    // FIXME: Update the permission to summary-report/templates/{templateId}
    @PreAuthorize("hasAnyLicenses(#agencyId, 'report') " +
            "&& hasPermission('summary-report/templates', 'READ', @summaryTemplateAuthorizationHelper" +
            ".getAgencyId(#agencyId, #templateId), null)")
    public ResponseEntity<SummaryTemplateCoreDataResultObject> getCoreData(Integer agencyId,
                                                                           Long templateId) {
        SummaryTemplateCoreDataResultObject resultObject;

        try {
            resultObject = summaryReportTemplateService.getReportTemplateCoreData(templateId);
        } catch (Exception ex) {
            resultObject = new SummaryTemplateCoreDataResultObject(null,
                    SummaryTemplateCoreDataResultObject.StatusCode.ERROR);
            log.error(ERROR_OCCUR_MESSAGE, ex);
        }

        return ResponseUtil.wrapOrNotFound(resultObject);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    // FIXME: Update the permission to summary-report/templates/{templateId}
    @PreAuthorize("hasAnyLicenses(#agencyId, 'report') " +
            "&& hasPermission('summary-report/templates', 'READ', @summaryTemplateAuthorizationHelper" +
            ".getAgencyId(#agencyId, #templateId), null)")
    public ResponseEntity<TemplateScheduleResultObject> getTemplateScheduleData(Integer agencyId,
                                                                                Long templateId) {
        TemplateScheduleResultObject resultObject;

        try {
            resultObject = summaryReportTemplateService.getReportTemplateScheduleData(templateId);
        } catch (Exception ex) {
            resultObject = TemplateScheduleResultObject.error();
            log.error(ERROR_OCCUR_MESSAGE, ex);
        }

        return ResponseUtil.wrapOrNotFound(resultObject);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    @PreAuthorize("hasAnyLicenses(#agencyId, 'report') " +
            "&& hasPermission('summary-report/templates/{templateId}', 'UPDATE', @summaryTemplateAuthorizationHelper" +
            ".getAgencyOwnerId(#agencyId, #templateId))")
    public ResponseEntity<SummaryTemplateManipulateResultObject> updateCoreData(Integer agencyId,
                                                                                Long templateId,
                                                                                SummaryTemplateUpdateCoreDataRequestVO updateRequestVO) {
        SummaryTemplateManipulateResultObject resultObject;
        try {
            updateRequestVO.setAgencyId(agencyId);
            resultObject = summaryReportTemplateService.updateReportTemplateCoreData(templateId, updateRequestVO);
        } catch (Exception exception) {
            resultObject = new SummaryTemplateManipulateResultObject(
                    SummaryTemplateManipulateResultObject.SummaryTemplateManipulateStatusCode.ERROR);
            log.error(ERROR_OCCUR_MESSAGE, exception);
        }

        return ResponseUtil.wrapOrNotFound(resultObject);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    @PreAuthorize("hasAnyLicenses(#agencyId, 'report') " +
            "&& hasPermission('summary-report/templates/{templateId}', 'UPDATE', @summaryTemplateAuthorizationHelper" +
            ".getAgencyOwnerId(#agencyId, #templateId))")
    public ResponseEntity<SummaryTemplateManipulateResultObject> updateTemplateScheduleData(Integer agencyId,
                                                                                            Long templateId,
                                                                                            TemplateScheduleVO templateScheduleVO) {
        SummaryTemplateManipulateResultObject resultObject;
        try {
            resultObject = summaryReportTemplateService
                    .updateReportTemplateScheduleData(templateId, templateScheduleVO);
        } catch (Exception exception) {
            resultObject = new SummaryTemplateManipulateResultObject(
                    SummaryTemplateManipulateResultObject.SummaryTemplateManipulateStatusCode.ERROR);
            log.error(ERROR_OCCUR_MESSAGE, exception);
        }

        return ResponseUtil.wrapOrNotFound(resultObject);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    @PreAuthorize("hasAnyLicenses(#agencyId, 'report') " +
            "&& hasPermission('summary-report/templates/{templateId}', 'DELETE', @summaryTemplateAuthorizationHelper" +
            ".getAgencyOwnerId(#agencyId, #templateIds))")
    public ResponseEntity<SummaryTemplateManipulateResultObject> deletes(Integer agencyId,
                                                                         List<Long> templateIds) {
        SummaryTemplateManipulateResultObject resultObject;
        try {
            SummaryTemplateDeleteRequestVO deleteRequestVO = new SummaryTemplateDeleteRequestVO();
            deleteRequestVO.setTemplateIds(templateIds);
            resultObject = summaryReportTemplateService.deleteReportTemplates(deleteRequestVO);
        } catch (Exception exception) {
            resultObject = new SummaryTemplateManipulateResultObject(
                    SummaryTemplateManipulateResultObject.SummaryTemplateManipulateStatusCode.ERROR);
            log.error(ERROR_OCCUR_MESSAGE, exception);
        }

        return ResponseUtil.wrapOrNotFound(resultObject);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    @PreAuthorize("hasAnyLicenses(#agencyId, 'report') " +
            "&& hasPermission('summary-report/templates/{templateId}', 'UPDATE', @summaryTemplateAuthorizationHelper" +
            ".getAgencyOwnerId(#agencyId, #templateId))")
    public ResponseEntity<SummaryTemplateManipulateResultObject> updateIntersections(Integer agencyId,
                                                                                     Long templateId,
                                                                                     IntersectionIdsRequestVO intersectionIdsRequestVO) {
        SummaryTemplateManipulateResultObject resultObject;
        try {
            resultObject = summaryReportTemplateService.updateIntersections(templateId, intersectionIdsRequestVO);
        } catch (Exception exception) {
            resultObject = new SummaryTemplateManipulateResultObject(
                    SummaryTemplateManipulateResultObject.SummaryTemplateManipulateStatusCode.ERROR);
            log.error(ERROR_OCCUR_MESSAGE, exception);
        }

        return ResponseUtil.wrapOrNotFound(resultObject);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    @PreAuthorize("hasAnyLicenses(#agencyId, 'report') " +
            "&& hasPermission('summary-report/templates/{templateId}', 'UPDATE', @summaryTemplateAuthorizationHelper" +
            ".getAgencyOwnerId(#agencyId, #activeRequestVO.templateIds))")
    public ResponseEntity<SummaryTemplateManipulateResultObject> activate(
            Integer agencyId,
            SummaryTemplateActivateRequestVO activeRequestVO) {
        SummaryTemplateManipulateResultObject resultObject;
        try {
            resultObject = summaryReportTemplateService.activateReportTemplates(activeRequestVO);
        } catch (Exception exception) {
            resultObject = new SummaryTemplateManipulateResultObject(
                    SummaryTemplateManipulateResultObject.SummaryTemplateManipulateStatusCode.ERROR);
            log.error(ERROR_OCCUR_MESSAGE, exception);
        }

        return ResponseUtil.wrapOrNotFound(resultObject);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    @PreAuthorize("hasAnyLicenses(#agencyId, 'report') " +
            "&& hasPermission('summary-report/templates/{templateId}', 'READ', @summaryTemplateAuthorizationHelper" +
            ".getAgencyOwnerId(#agencyId, #templateId))")
    public ResponseEntity<IntersectionSearchResultObject> searchTemplateIntersections(Integer agencyId,
                                                                                      Long templateId,
                                                                                      String text,
                                                                                      String status,
                                                                                      String[] orderByColumns,
                                                                                      Integer page,
                                                                                      Integer size) {
        IntersectionSearchResultObject resultObject;

        TemplateIntersectionSearchRequestVO searchRequestVO = TemplateIntersectionSearchRequestVO.builder()
                .templateId(templateId)
                .text(text)
                .status(status)
                .orderByColumns(orderByColumns)
                .page(page)
                .size(size)
                .build();

        try {
            resultObject = summaryReportTemplateService.searchTemplateIntersections(searchRequestVO);
        } catch (Exception exception) {
            resultObject = new IntersectionSearchResultObject(null, IntersectionSearchResultObject.StatusCode.ERROR);
            log.error(ERROR_OCCUR_MESSAGE, exception);
        }

        return ResponseUtil.wrapOrNotFound(resultObject);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    @PreAuthorize("hasAnyLicenses(#agencyId, 'report') " +
            "&& hasPermission('summary-report/templates', 'READ', #agencyId, null)")
    public ResponseEntity<SummaryTemplateSearchResultObject> search(Integer agencyId,
                                                                    String text,
                                                                    Long fromDate,
                                                                    Long toDate,
                                                                    DayOfWeek[] weekDays,
                                                                    TemplateAggregation aggregation,
                                                                    Long ownerId,
                                                                    TemplateStatus status,
                                                                    String[] orderByColumns,
                                                                    Integer page,
                                                                    Integer size) {
        SummaryTemplateSearchResultObject resultObject;

        try {
            Timestamp createdAtFrom = (fromDate != null) ? new Timestamp(fromDate) : null;
            Timestamp createdAtTo = (toDate != null) ? new Timestamp(toDate) : null;

            Set<DayOfWeek> weekDaySet = new HashSet<>();
            if (weekDays != null) {
                for (DayOfWeek weekDay : weekDays) {
                    if (weekDay != null)
                        weekDaySet.add(weekDay);
                }
            }

            SummaryTemplateSearchRequestVO requestVO = SummaryTemplateSearchRequestVO.builder()
                    .agencyId(agencyId)
                    .createdAtFrom(createdAtFrom)
                    .createdAtTo(createdAtTo)
                    .orderByColumns(orderByColumns)
                    .aggregation(aggregation)
                    .ownerId(ownerId)
                    .weekDays(weekDaySet)
                    .page(page)
                    .size(size)
                    .status(status)
                    .text(text)
                    .build();

            resultObject = summaryReportTemplateService.searchReportTemplates(requestVO);
        } catch (Exception exception) {
            resultObject = SummaryTemplateSearchResultObject.error();
            log.error(ERROR_OCCUR_MESSAGE, exception);
        }

        return ResponseUtil.wrapOrNotFound(resultObject);
    }

    @Override
    @PreAuthorize("hasAnyLicenses(#agencyId, 'report') " +
            "&& hasPermission('summary-report/templates', 'READ', @summaryTemplateAuthorizationHelper" +
            ".getAgencyId(#agencyId, #templateId), null)")
    public ResponseEntity<SummaryTemplateProcessResultObject> runSummaryTemplate(Integer agencyId,
                                                                                 Long templateId) {
        SummaryTemplateProcessResultObject resultObject;
        try {
            resultObject = summaryReportTemplateService.runReportTemplate(templateId);
        } catch (Exception e) {
            resultObject = new SummaryTemplateProcessResultObject(StatusCode.ERROR);
            log.error(ERROR_OCCUR_MESSAGE, e);
        }

        return ResponseUtil.wrapOrNotFound(resultObject);
    }

    @Override
    @PreAuthorize("hasAnyLicenses(#agencyId, 'report') " +
            "&& hasPermission('summary-report/templates', 'READ', #agencyId, null)")
    public ResponseEntity<SummaryMetricResultObject> getAllMetrics(Integer agencyId) {
        SummaryMetricResultObject resultObject;

        try {
            resultObject = summaryReportTemplateService.getAllMetrics();
        } catch (Exception e) {
            log.error(ERROR_OCCUR_MESSAGE, e);
            resultObject = new SummaryMetricResultObject(SimpleResultObject.SimpleStatusCode.SUCCESS);
        }

        return ResponseUtil.wrapOrNotFound(resultObject);
    }

}
