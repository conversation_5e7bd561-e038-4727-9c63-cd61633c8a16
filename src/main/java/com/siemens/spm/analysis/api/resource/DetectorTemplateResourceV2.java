package com.siemens.spm.analysis.api.resource;

import java.sql.Timestamp;
import java.time.DayOfWeek;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import com.siemens.spm.common.agency.utils.AgencyUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.RestController;

import com.siemens.spm.analysis.api.boundary.DetectorReportTemplateService;
import com.siemens.spm.analysis.api.controller.DetectorTemplateControllerV2;
import com.siemens.spm.analysis.api.vo.detectorreport.DetectorTemplateScheduleVO;
import com.siemens.spm.analysis.api.vo.enums.TemplateAggregation;
import com.siemens.spm.analysis.api.vo.enums.TemplateStatus;
import com.siemens.spm.analysis.api.vo.request.IntersectionIdsRequestVO;
import com.siemens.spm.analysis.api.vo.request.TemplateIntersectionSearchRequestVO;
import com.siemens.spm.analysis.api.vo.request.detectorreport.DetectorTemplateActivateRequestVO;
import com.siemens.spm.analysis.api.vo.request.detectorreport.DetectorTemplateCreateRequestVO;
import com.siemens.spm.analysis.api.vo.request.detectorreport.DetectorTemplateSearchRequestVO;
import com.siemens.spm.analysis.api.vo.request.detectorreport.DetectorTemplateUpdateCoreDataRequestVO;
import com.siemens.spm.analysis.api.vo.response.IntersectionSearchResultObject;
import com.siemens.spm.analysis.api.vo.response.detectorreport.DetectorMetricResultObject;
import com.siemens.spm.analysis.api.vo.response.detectorreport.DetectorScheduleResultObject;
import com.siemens.spm.analysis.api.vo.response.detectorreport.DetectorTemplateCoreDataResultObject;
import com.siemens.spm.analysis.api.vo.response.detectorreport.DetectorTemplateDetailResultObject;
import com.siemens.spm.analysis.api.vo.response.detectorreport.DetectorTemplateManipulateResultObject;
import com.siemens.spm.analysis.api.vo.response.detectorreport.DetectorTemplateProcessResultObject;
import com.siemens.spm.analysis.api.vo.response.detectorreport.DetectorTemplateSearchResultObject;
import com.siemens.spm.common.shared.vo.SimpleResultObject;
import com.siemens.spm.common.util.ResponseUtil;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@RestController
// TODO: Update the permission corresponding to the new permission system detector-report/results
public class DetectorTemplateResourceV2 implements DetectorTemplateControllerV2 {

    private final DetectorReportTemplateService detectorReportTemplateService;

    public DetectorTemplateResourceV2(DetectorReportTemplateService detectorReportTemplateService) {
        this.detectorReportTemplateService = detectorReportTemplateService;
    }

    @Override
    @PreAuthorize("""
            hasAnyLicenses(#agencyId, 'report') &&
            hasPermission('summary-report/templates', 'CREATE', #agencyId, null)
            """
    )
    public ResponseEntity<DetectorTemplateDetailResultObject> create(
            Integer agencyId,
            DetectorTemplateCreateRequestVO requestVO) {
        DetectorTemplateDetailResultObject resultObject;
        try {
            requestVO.getGeneralVO().setAgencyId(Integer.valueOf(AgencyUtils.getAgencyId()));
            resultObject = detectorReportTemplateService.createReportTemplate(requestVO);
        } catch (Exception ex) {
            log.error("Couldn't create detector template", ex);
            resultObject = new DetectorTemplateDetailResultObject(null,
                    DetectorTemplateDetailResultObject.StatusCode.ERROR);
        }
        return ResponseUtil.wrapOrNotFound(resultObject);
    }

    @Override
    @PreAuthorize("""
            hasAnyLicenses(#agencyId, 'report') &&
             hasPermission('summary-report/templates', 'READ', #agencyId, null)
            """
    )
    public ResponseEntity<IntersectionSearchResultObject> searchAvailableTemplateIntersections(Integer agencyId,
                                                                                               Long templateId,
                                                                                               String[] excludeIntIds,
                                                                                               String text,
                                                                                               String[] sort,
                                                                                               Boolean pagination,
                                                                                               Integer page,
                                                                                               Integer size) {
        IntersectionSearchResultObject resultObject;
        TemplateIntersectionSearchRequestVO searchRequestVO = TemplateIntersectionSearchRequestVO.builder()
                .agencyId(agencyId)
                .templateId(templateId)
                .orderByColumns(sort)
                .excludeIntIds(excludeIntIds)
                .shouldPaginate(pagination)
                .text(text)
                .page(page)
                .size(size)
                .build();
        try {
            resultObject = detectorReportTemplateService.searchAvailableTemplateIntersections(searchRequestVO);
        } catch (Exception exception) {
            log.error("Couldn't search available template intersection", exception);
            resultObject = new IntersectionSearchResultObject(null, IntersectionSearchResultObject.StatusCode.ERROR);
        }

        return ResponseUtil.wrapOrNotFound(resultObject);
    }

    @Override
    @PreAuthorize("""
            hasAnyLicenses(#agencyId, 'report') &&
             hasPermission('summary-report/templates', 'READ', @detectorReportTemplateAuthorizationHelper.getAgencyOwnerId(#agencyId, #templateId))
            """
    )
    public ResponseEntity<DetectorTemplateCoreDataResultObject> getCoreData(
            Integer agencyId,
            Long templateId) {
        DetectorTemplateCoreDataResultObject resultObject;
        try {
            resultObject = detectorReportTemplateService.getTemplateCoreData(templateId);
        } catch (Exception exception) {
            log.error("Couldn't get template core data", exception);
            resultObject = new DetectorTemplateCoreDataResultObject(null,
                    DetectorTemplateCoreDataResultObject.StatusCode.ERROR);
        }
        return ResponseUtil.wrapOrNotFound(resultObject);
    }

    @Override
    @PreAuthorize("""
        hasAnyLicenses(#agencyId, 'report')
            && hasPermission('summary-report/templates', 'READ', @detectorReportTemplateAuthorizationHelper.getAgencyOwnerId(#agencyId, #templateId))
        """
    )
    public ResponseEntity<DetectorScheduleResultObject> getTemplateScheduleData(Integer agencyId, Long templateId) {
        DetectorScheduleResultObject resultObject;
        try {
            resultObject = detectorReportTemplateService.getTemplateSchedule(templateId);
        } catch (Exception exception) {
            log.error("Couldn't get template schedule data", exception);
            resultObject = new DetectorScheduleResultObject(null,
                    DetectorScheduleResultObject.StatusCode.ERROR);
        }
        return ResponseUtil.wrapOrNotFound(resultObject);
    }

    @Override
    @PreAuthorize("""
        hasAnyLicenses(#agencyId, 'report') &&
         hasPermission('summary-report/templates/{templateId}', 'UPDATE', @detectorReportTemplateAuthorizationHelper.getAgencyOwnerId(#agencyId, #templateId))
        """
    )
    public ResponseEntity<DetectorTemplateManipulateResultObject> updateCoreData(Integer agencyId,
                                                                                 Long templateId,
                                                                                 DetectorTemplateUpdateCoreDataRequestVO updateRequestVO) {
        DetectorTemplateManipulateResultObject resultObject;
        try {
            resultObject = detectorReportTemplateService.updateCoreData(templateId, updateRequestVO);
        } catch (Exception ex) {
            log.error("Couldn't update core data", ex);
            resultObject = DetectorTemplateManipulateResultObject.build(
                    DetectorTemplateManipulateResultObject.StatusCode.ERROR);
        }
        return ResponseUtil.wrapOrNotFound(resultObject);
    }

    @Override
    @PreAuthorize("""
            hasAnyLicenses(#agencyId, 'report') &&
            hasPermission('summary-report/templates/{templateId}', 'UPDATE', @detectorReportTemplateAuthorizationHelper.getAgencyOwnerId(#agencyId, #templateId))
            """
    )
    public ResponseEntity<DetectorTemplateManipulateResultObject> updateTemplateScheduleData(Integer agencyId,
                                                                                             Long templateId,
                                                                                             DetectorTemplateScheduleVO templateScheduleVO) {
        DetectorTemplateManipulateResultObject resultObject;
        try {
            resultObject = detectorReportTemplateService.updateTemplateScheduleData(templateId, templateScheduleVO);
        } catch (Exception exception) {
            log.error("Couldn't update template schedule data", exception);
            resultObject = new DetectorTemplateManipulateResultObject(
                    DetectorTemplateManipulateResultObject.StatusCode.ERROR
            );
        }
        return ResponseUtil.wrapOrNotFound(resultObject);
    }

    @Override
    @PreAuthorize("""
            hasAnyLicenses(#agencyId, 'report') &&
            hasPermission('summary-report/templates/{templateId}', 'DELETE', @detectorReportTemplateAuthorizationHelper.getAgencyOwnerId(#agencyId, #templateIds))
            """
    )
    public ResponseEntity<DetectorTemplateManipulateResultObject> deletes(Integer agencyId,
                                                                          List<Long> templateIds) {
        DetectorTemplateManipulateResultObject resultObject;
        try {
            resultObject = detectorReportTemplateService.softDeletes(templateIds);
        } catch (Exception exception) {
            log.error("Couldn't delete templates", exception);
            resultObject = new DetectorTemplateManipulateResultObject(
                    DetectorTemplateManipulateResultObject.StatusCode.ERROR
            );
        }
        return ResponseUtil.wrapOrNotFound(resultObject);
    }

    @PreAuthorize("""
            hasAnyLicenses(#agencyId, 'report') &&
            hasPermission('summary-report/templates/{templateId}', 'UPDATE', @detectorReportTemplateAuthorizationHelper.getAgencyOwnerId(#agencyId, #templateId))
            """
    )
    public ResponseEntity<DetectorTemplateManipulateResultObject> updateIntersections(Integer agencyId,
                                                                                      Long templateId,
                                                                                      IntersectionIdsRequestVO intersectionIdsRequestVO) {
        DetectorTemplateManipulateResultObject resultObject;
        try {
            resultObject = detectorReportTemplateService.updateIntersections(templateId, intersectionIdsRequestVO);
        } catch (Exception exception) {
            log.error("Couldn't update intersections", exception);
            resultObject = new DetectorTemplateManipulateResultObject(
                    DetectorTemplateManipulateResultObject.StatusCode.ERROR);
        }
        return ResponseUtil.wrapOrNotFound(resultObject);
    }

    @Override
    @PreAuthorize("""
            hasAnyLicenses(#agencyId, 'report') &&
            hasPermission('summary-report/templates', 'CREATE', #agencyId, null)
            """
    )
    public ResponseEntity<DetectorTemplateManipulateResultObject> activate(Integer agencyId,
                                                                           DetectorTemplateActivateRequestVO activeRequestVO) {
        DetectorTemplateManipulateResultObject resultObject;
        try {
            resultObject = detectorReportTemplateService.activateTemplates(activeRequestVO);
        } catch (Exception exception) {
            log.error("Couldn't activate or deactivate template", exception);
            resultObject = new DetectorTemplateManipulateResultObject(
                    DetectorTemplateManipulateResultObject.StatusCode.ERROR);
        }
        return ResponseUtil.wrapOrNotFound(resultObject);
    }

    @Override
    @PreAuthorize("""
        hasAnyLicenses(#agencyId, 'report')
            && hasPermission('summary-report/templates', 'READ', @detectorReportTemplateAuthorizationHelper.getAgencyOwnerId(#agencyId, #templateId))
        """
    )
    public ResponseEntity<IntersectionSearchResultObject> searchTemplateIntersections(Integer agencyId,
                                                                                      Long templateId,
                                                                                      String text,
                                                                                      String status,
                                                                                      String[] orderByColumns,
                                                                                      Integer page,
                                                                                      Integer size,
                                                                                      Boolean pagination) {
        IntersectionSearchResultObject resultObject;
        TemplateIntersectionSearchRequestVO searchRequestVO = TemplateIntersectionSearchRequestVO.builder()
                .templateId(templateId)
                .text(text)
                .status(status)
                .orderByColumns(orderByColumns)
                .page(page)
                .size(size)
                .shouldPaginate(pagination)
                .build();

        try {
            resultObject = detectorReportTemplateService.searchTemplateIntersections(searchRequestVO);
        } catch (Exception exception) {
            log.error("Couldn't search template intersections", exception);
            resultObject = new IntersectionSearchResultObject(null, IntersectionSearchResultObject.StatusCode.ERROR);
        }
        return ResponseUtil.wrapOrNotFound(resultObject);
    }

    @Override
    @PreAuthorize("""
        hasAnyLicenses(#agencyId, 'report')
            && hasPermission('summary-report/templates', 'READ', #agencyId, null)
        """
    )
    public ResponseEntity<DetectorTemplateSearchResultObject> search(Integer agencyId,
                                                                     String text,
                                                                     Long fromDate,
                                                                     Long toDate,
                                                                     DayOfWeek[] weekDays,
                                                                     TemplateAggregation aggregation,
                                                                     Long ownerId,
                                                                     TemplateStatus status,
                                                                     String[] orderByColumns,
                                                                     Integer page,
                                                                     Integer size) {
        DetectorTemplateSearchResultObject resultObject;

        try {
            Timestamp createdAtFrom = (fromDate != null) ? new Timestamp(fromDate) : null;
            Timestamp createdAtTo = (toDate != null) ? new Timestamp(toDate) : null;

            Set<DayOfWeek> weekDaySet = new HashSet<>();
            if (weekDays != null) {
                for (DayOfWeek weekDay : weekDays) {
                    if (weekDay != null)
                        weekDaySet.add(weekDay);
                }
            }

            DetectorTemplateSearchRequestVO requestVO = DetectorTemplateSearchRequestVO.builder()
                    .agencyId(agencyId)
                    .createdAtFrom(createdAtFrom)
                    .createdAtTo(createdAtTo)
                    .orderByColumns(orderByColumns)
                    .aggregation(aggregation)
                    .ownerId(ownerId)
                    .weekDays(weekDaySet)
                    .page(page)
                    .size(size)
                    .status(status)
                    .text(text)
                    .build();
            resultObject = detectorReportTemplateService.searchTemplates(requestVO);
        } catch (Exception exception) {
            log.error("Couldn't search tmeplate by filter", exception);
            resultObject = DetectorTemplateSearchResultObject.error();
        }
        return ResponseUtil.wrapOrNotFound(resultObject);
    }

    @Override
    @PreAuthorize("""
        hasAnyLicenses(#agencyId, 'report')
            && hasPermission('summary-report/templates', 'READ', #agencyId, null)
        """
    )
    public ResponseEntity<DetectorMetricResultObject> getAllMetrics(Integer agencyId) {
        DetectorMetricResultObject resultObject;
        try {
            resultObject = detectorReportTemplateService.getAllMetrics();
        } catch (Exception ex) {
            log.error("Couldn't get template metrics", ex);
            resultObject = new DetectorMetricResultObject(SimpleResultObject.SimpleStatusCode.ERROR);
        }
        return ResponseUtil.wrapOrNotFound(resultObject);
    }

    @Override
    @PreAuthorize("""
        hasAnyLicenses(#agencyId, 'report')
            && hasPermission('summary-report/templates', 'READ', @detectorReportTemplateAuthorizationHelper.getAgencyOwnerId(#agencyId, #templateId))
        """
    )
    public ResponseEntity<DetectorTemplateProcessResultObject> runTemplate(Integer agencyId,
                                                                           Long templateId) {
        DetectorTemplateProcessResultObject resultObject;
        try {
            resultObject = detectorReportTemplateService.runTemplate(templateId);
        } catch (Exception e) {
            log.error("Couldn't run template manually", e);
            resultObject = new DetectorTemplateProcessResultObject(
                    DetectorTemplateProcessResultObject.StatusCode.ERROR);
        }
        return ResponseUtil.wrapOrNotFound(resultObject);
    }
}
